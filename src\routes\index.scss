* {
    box-sizing: border-box;
}

:root {
    --node-transition: 0.5s cubic-bezier(0.5,0,0,1);
}

@font-face {
    font-family: "Kingthings Organica";
    src: url("/kingthings-organica.extra-bold.ttf");
}

body {
    margin: 0;
    width: 100vw;
    height: 100vh;

    font-family: "Atkinson Hyperlegible Next", sans-serif;
    --font-strong: "Kingthings Organica", "Atkinson Hyperlegible Next", sans-serif;

    color: oklch(0.3 0.1 250);
    background: linear-gradient(
        0.25turn in oklch,
        oklch(0.9 0.04 190),
        oklch(0.9 0.02 250),
        oklch(0.9 0.04 330)
    );
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: var(--font-strong);
    color: oklch(0.3 0.12 210);
}

input,
button,
textarea {
    font-family: inherit;
    font-size: 1em;
    padding: 0;
    margin: 0;
    border: none;
    background: none;
}


.ouroboard-button {
	padding: 0.25rem;
	font-weight: 400;

    background: oklch(1 0 0);
	border: 1px solid #0000001f;
	height: max-content;

	&:hover {
		cursor: pointer;
		background: #0000000f;
	}
}

/* xyflow theme files. Delete these to start from our base */

div.svelte-flow {
    --xy-background-color: #0000;
    --background-color-default: #0000;
    /* Built-in Variables see https://svelteflow.dev/learn/customization/theming */
    --xy-node-border-default: 1px solid #ededed;

    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px #00000005, 0px 3.54px 4.55px 0px #0000000d,
        0px 0.51px 1.01px 0px #0000001a;

    --xy-node-border-radius-default: 8px;

    --xy-handle-background-color-default: #ffffff;
    --xy-handle-border-color-default: #aaaaaa;

    --xy-edge-label-color-default: #505050;

    --xy-resize-background-color-default: #9e86ed;
}

div.svelte-flow.dark {
    --xy-node-boxshadow-default:
        0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.05),
        /* light shadow */
        0px 3.54px 4.55px 0px rgba(255, 255, 255, 0.13),
        /* medium shadow */
        0px 0.51px 1.01px 0px rgba(255, 255, 255, 0.2);
    /* smallest shadow */
    --xy-theme-color-focus: #535353;
}

/* Customizing Default Theming */

div.svelte-flow__node {
    will-change: transform;
    
    &:has(task-card.mounted) {
        transition: transform var(--node-transition);
    }
}

div.svelte-flow__node.selectable:focus {
    box-shadow: 0px 0px 0px 4px var(--xy-theme-color-focus);
    border-color: #d9d9d9;
}

div.svelte-flow__node.selectable:focus:active {
    box-shadow: var(--xy-node-boxshadow-default);
}

div.svelte-flow__node.selectable:hover,
div.svelte-flow__node.draggable:hover {
    border-color: var(--xy-theme-hover);
}

div.svelte-flow__node.selectable.selected {
    border-color: var(--xy-theme-selected);
    box-shadow: var(--xy-node-boxshadow-default);
}

div.svelte-flow__node-group {
    background-color: rgba(207, 182, 255, 0.4);
    border-color: #9e86ed;
}

g.svelte-flow__edge.selectable:hover .svelte-flow__edge-path,
g.svelte-flow__edge.selectable.selected .svelte-flow__edge-path {
    stroke: oklch(0.5 0.1 220);
}

path.svelte-flow__edge-path,
path.svelte-flow__edge-interaction {
    transition: d var(--node-transition);
}

div.svelte-flow__handle {
    border-color: #aaa;
    background-color: #fff;
    width: 1rem;
    height: 1rem;
}

div.svelte-flow__handle-left {
    left: -0.25rem;
}

div.svelte-flow__handle-right {
    right: -0.25rem;
}

.svelte-flow__handle.connectionindicator:hover {
    pointer-events: all;
    border-color: var(--xy-theme-edge-hover);
    background-color: white;
}

.svelte-flow__handle.connectionindicator:focus,
.svelte-flow__handle.connectingfrom,
.svelte-flow__handle.connectingto {
    border-color: var(--xy-theme-edge-hover);
}

.svelte-flow__node:has(.svelte-flow__resize-control.line) {
    border-radius: 0;
}

.svelte-flow__resize-control.handle {
    border: none;
    border-radius: 0;
    width: 5px;
    height: 5px;
}

div.svelte-flow__edge-label {
    background: none;
}

/* 
  Custom Example CSS  - This CSS is to improve the example experience.
  You can remove it if you want to use the default styles.
 
  New Theme Classes:
    .xy-theme__button   - Styles for buttons.
    .xy-theme__input    - Styles for text inputs.
    .xy-theme__checkbox - Styles for checkboxes.
    .xy-theme__select   - Styles for dropdown selects.
    .xy-theme__label    - Styles for labels.
  
  Use these classes to apply consistent theming across your components.
*/

:root {
    --color-primary: #ff4000;
    --color-background: #fefefe;
    --color-hover-bg: #f6f6f6;
    --color-disabled: #76797e;
}

/* Custom Button Styling */
.xy-theme__button {
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 2.5rem;
    padding: 0 1rem;
    border-radius: 100px;
    border: 1px solid var(--color-primary);
    background-color: var(--color-background);
    color: var(--color-primary);
    transition:
        background-color 0.2s ease,
        border-color 0.2s ease;
    box-shadow: var(--xy-node-boxshadow-default);
}

.xy-theme__button:hover {
    background-color: var(--xy-controls-button-background-color-hover-default);
}

.xy-theme__button:active {
    background-color: var(--color-hover-bg);
}

.xy-theme__button:disabled {
    color: var(--color-disabled);
    opacity: 0.8;
    cursor: not-allowed;
    border: 1px solid var(--color-disabled);
}

.xy-theme__button>span {
    margin-right: 0.2rem;
}

/* Add gap between adjacent buttons */
.xy-theme__button+.xy-theme__button {
    margin-left: 0.3rem;
}

/* Example Input Styling */
.xy-theme__input {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--color-primary);
    border-radius: 7px;
    background-color: var(--color-background);
    transition:
        background-color 0.2s ease,
        border-color 0.2s ease;
    font-size: 1rem;
    color: inherit;
}

.xy-theme__input:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(255, 0, 115, 0.3);
}

/* Specific Checkbox Styling */
.xy-theme__checkbox {
    appearance: none;
    -webkit-appearance: none;
    width: 1.25rem;
    height: 1.25rem;
    border-radius: 7px;
    border: 2px solid var(--color-primary);
    background-color: var(--color-background);
    transition:
        background-color 0.2s ease,
        border-color 0.2s ease;
    cursor: pointer;
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.5rem;
}

.xy-theme__checkbox:checked {
    background-color: var(--color-primary);
    border-color: var(--color-primary);
}

.xy-theme__checkbox:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(255, 0, 115, 0.3);
}

/* Dropdown Styling */
.xy-theme__select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--color-primary);
    border-radius: 50px;
    background-color: var(--color-background);
    transition:
        background-color 0.2s ease,
        border-color 0.2s ease;
    font-size: 1rem;
    color: inherit;
    margin-right: 0.5rem;
    box-shadow: var(--xy-node-boxshadow-default);
}

.xy-theme__select:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(255, 0, 115, 0.3);
}

.xy-theme__label {
    margin-top: 10px;
    margin-bottom: 3px;
    display: inline-block;
}