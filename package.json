{"name": "ouroboard", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:start": "docker compose up", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test"}, "devDependencies": {"@internationalized/date": "^3.8.2", "@lucide/svelte": "^0.532.0", "@playwright/test": "^1.49.1", "@sveltejs/adapter-auto": "^6.0.1", "@sveltejs/kit": "^2.26.1", "@sveltejs/vite-plugin-svelte": "^6.1.0", "@tailwindcss/vite": "^4.1.11", "@vaie/hui": "^0.0.0", "@vitest/browser": "^3.2.3", "bits-ui": "^2.8.13", "clsx": "^2.1.1", "drizzle-kit": "^0.31.4", "playwright": "^1.53.0", "sass": "^1.89.2", "svelte": "^5.37.1", "svelte-check": "^4.3.0", "tailwind-merge": "^3.3.1", "tailwind-variants": "^2.0.1", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.8.3", "vite": "^7.0.6", "vitest": "^3.2.3", "vitest-browser-svelte": "^0.1.0"}, "dependencies": {"@dagrejs/dagre": "^1.1.5", "@xyflow/svelte": "^1.2.2", "drizzle-orm": "^0.44.3", "postgres": "^3.4.7"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}