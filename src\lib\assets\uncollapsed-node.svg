<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="512"
   height="512"
   viewBox="0 0 135.46666 135.46667"
   version="1.1"
   id="svg8"
   inkscape:version="1.3 (0e150ed6c4, 2023-07-21)"
   xml:space="preserve"
   inkscape:export-filename="..\..\..\..\Dev\GitHub\ouroboard\src\lib\assets\collapsed-node.svg"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96"
   sodipodi:docname="uncollapsed-node.svg"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:dc="http://purl.org/dc/elements/1.1/"><defs
     id="defs2" /><sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="3.959798"
     inkscape:cx="211.37442"
     inkscape:cy="138.76971"
     inkscape:document-units="px"
     inkscape:current-layer="layer2"
     inkscape:document-rotation="0"
     showgrid="true"
     units="px"
     inkscape:pagecheckerboard="true"
     borderlayer="false"
     inkscape:showpageshadow="0"
     inkscape:deskcolor="#d1d1d1"
     inkscape:window-width="2560"
     inkscape:window-height="1494"
     inkscape:window-x="-11"
     inkscape:window-y="-11"
     inkscape:window-maximized="1"><inkscape:grid
       type="xygrid"
       id="grid2405"
       originx="0"
       originy="0"
       spacingy="1"
       spacingx="1"
       units="px"
       visible="true" /></sodipodi:namedview><metadata
     id="metadata5"><rdf:RDF><cc:Work
         rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" /></cc:Work></rdf:RDF></metadata><g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     style="display:none"><path
       style="fill:#000000;stroke-width:1.57478"
       d="m 69.750354,86.854978 c -1.72114,-2.712494 -3.04463,-5.023049 -3.28086,-7.85787 -2.55134,-0.566965 -8.35902,-1.99316 -17.312552,-2.126795 C 25.04452,76.510426 20.241342,76.774253 14.498155,76.13445 6.6776063,75.263143 4.4104513,74.80323 3.1724363,73.836931 c -0.836479,-0.652887 -0.877711,-0.89304 -0.515305,-3.001304 0.216663,-1.260388 0.209779,-2.898705 0.331187,-3.640709 0.121856,-0.744775 0.585646,-1.544367 1.035379,-1.785056 1.996287,-1.068375 14.0040547,-2.110027 28.3908177,-2.497186 8.020696,-0.215854 16.66396,-0.456541 22.514819,-0.672446 6.60402,-0.243697 6.68413,-0.39611 10.76371,-1.541942 0.23624,-3.307292 1.169,-5.873992 2.23255,-7.507862 3.24535,-4.985634 7.49515,-8.228469 14.90771,-12.216938 2.8682,-1.543272 8.15605,-2.691043 14.75291,-2.66765 8.298376,0.02947 11.827476,1.199762 19.814796,6.317318 5.60198,3.589263 9.49127,13.647777 9.47696,23.219912 -0.0213,14.328187 -4.07798,18.093021 -11.92953,24.319552 -3.99013,3.16429 -8.15342,4.88785 -16.381266,5.12164 -7.18037,0.20404 -10.6668,-0.23537 -14.50036,-1.28979 -8.42453,-2.31717 -11.87965,-5.299127 -14.31646,-9.139492 z"
       id="path2-9"
       sodipodi:nodetypes="scsssssssscsssssssss" /></g><g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Layer 2"
     style="display:inline"><path
       style="display:inline;fill:#000000"
       d="M 104.52293,124.93028 C 89.658331,122.15022 63.756889,110.23253 57.433715,73.952045 55.005947,60.022241 51.027137,42.133778 46.470206,35.559987 42.905755,30.417939 36.708088,26.759847 32.60793,25.825861 24.536381,23.987222 16.09293,22.838915 12.022633,22.070653 8.0055044,21.312426 5.8439704,20.480827 5.4741194,19.551265 c -0.05701,-0.143294 0.006,-1.276699 0.140067,-2.518681 0.134049,-1.24198 0.306804,-2.856963 0.383898,-3.588851 0.07738,-0.734631 0.371893,-1.523339 0.657477,-1.76075 1.281734,-1.065526 8.7162156,-2.082198 18.1130486,-2.476976 8.833078,-0.371092 53.237991,0.183148 69.44794,0.0011 7.00313,-0.07864 11.23537,-0.298341 17.50822,-0.380529 4.20818,-0.05514 7.24324,-0.269776 9.90876,-0.151513 1.80034,0.07988 3.27895,0.09198 4.62553,0.668125 0.83927,0.359087 1.26001,1.43337 1.22708,2.377908 -0.0258,0.739789 -0.81548,3.607678 -0.67541,4.623872 0.0302,0.219034 0.4345,3.043525 0.16908,3.481227 -1.0579,1.744578 -5.51762,3.415858 -10.45858,3.33987 -17.540811,-0.269763 -47.572267,-0.205712 -53.708015,4.668627 0.947055,15.810934 20.682539,25.874651 33.87773,28.320821 1.7944,0.332652 14.522145,1.172952 15.622325,1.201671 7.23352,0.188826 9.70749,0.385289 11.04801,0.46888 2.84191,0.177213 3.79426,0.774447 6.19648,2.622715 0.87971,0.676845 0.22589,1.333875 0.16667,2.282478 -0.0839,1.339332 0.14842,2.980027 0.005,3.64782 -0.33597,1.564295 -1.59055,3.579475 -4.82425,3.672711 -4.51222,0.130099 -12.50343,0.22736 -19.56695,0.157932 -10.673948,-0.104914 -28.056949,-4.296015 -32.88635,-8.280334 0.755479,5.664239 2.436258,24.257856 11.530757,35.139402 6.123062,7.32622 13.096945,12.00587 25.686733,16.16574 9.37013,3.09605 14.51967,2.24343 16.06894,3.2032 1.01524,0.62893 1.96816,1.50306 1.93827,1.72644 -0.03,0.22337 0.0244,1.81955 -0.28056,2.62697 -0.34431,0.91153 -1.49098,3.13394 -1.43116,3.56429 0.0887,0.63832 0.0274,0.76297 -0.7767,1.57843 -0.708,0.71798 -1.06581,0.82077 -1.82834,0.84245 -7.59915,0.21615 -10.24768,-0.23966 -18.83689,-1.84605 z"
       id="path3"
       sodipodi:nodetypes="sssssscssssssssssscsssssssscssscssssss" /></g></svg>
